#!/usr/bin/env python3
"""
HTML Country Filter

This script loops through all HTML files in the /1/ folder and filters them by country.
It extracts country information from HTML files based on the specific pattern:
- Looks for "Country Name:" text
- Extracts the country name from the following span with class "font-bold text-capitalize"

Usage:
    python html_country_filter.py [--country COUNTRY_NAME] [--list-countries] [--summary]
"""

import os
import re
import argparse
from collections import defaultdict
from pathlib import Path


class HTMLCountryFilter:
    def __init__(self, folder_path="1"):
        """
        Initialize the HTML Country Filter
        
        Args:
            folder_path (str): Path to the folder containing HTML files
        """
        self.folder_path = Path(folder_path)
        self.files_by_country = defaultdict(list)
        self.processed_files = 0
        self.failed_files = []
        
    def extract_country_from_html(self, file_path):
        """
        Extract country name from an HTML file
        
        Args:
            file_path (Path): Path to the HTML file
            
        Returns:
            str or None: Country name if found, None otherwise
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()
                
            # Pattern to match the country section
            # Look for "Country Name:" followed by the country name in a span with "font-bold text-capitalize"
            pattern = r'Country Name:\s*<!----></span>.*?<span[^>]*class="[^"]*font-bold text-capitalize[^"]*"[^>]*>\s*([^<\s][^<]*?)\s*</span>'
            
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            if match:
                country = match.group(1).strip()
                return country
            
            return None
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return None
    
    def process_all_files(self):
        """
        Process all HTML files in the folder and extract country information
        """
        if not self.folder_path.exists():
            print(f"Error: Folder '{self.folder_path}' does not exist.")
            return
        
        html_files = list(self.folder_path.glob("*.html"))
        
        if not html_files:
            print(f"No HTML files found in '{self.folder_path}'")
            return
        
        print(f"Processing {len(html_files)} HTML files...")
        
        for file_path in html_files:
            country = self.extract_country_from_html(file_path)
            
            if country:
                self.files_by_country[country].append(file_path.name)
                self.processed_files += 1
            else:
                self.failed_files.append(file_path.name)
        
        print(f"Successfully processed {self.processed_files} files")
        if self.failed_files:
            print(f"Failed to extract country from {len(self.failed_files)} files")
    
    def get_files_by_country(self, country_name):
        """
        Get all files for a specific country
        
        Args:
            country_name (str): Name of the country
            
        Returns:
            list: List of file names for the specified country
        """
        # Case-insensitive search
        for country, files in self.files_by_country.items():
            if country.lower() == country_name.lower():
                return files
        return []
    
    def list_all_countries(self):
        """
        List all countries found in the HTML files
        
        Returns:
            list: Sorted list of country names
        """
        return sorted(self.files_by_country.keys())
    
    def print_summary(self):
        """
        Print a summary of files grouped by country
        """
        if not self.files_by_country:
            print("No countries found. Make sure to run process_all_files() first.")
            return
        
        print("\n" + "="*60)
        print("SUMMARY: Files grouped by Country")
        print("="*60)
        
        for country in sorted(self.files_by_country.keys()):
            files = self.files_by_country[country]
            print(f"\n{country}: {len(files)} files")
            for file_name in sorted(files):
                print(f"  - {file_name}")
        
        print(f"\nTotal countries: {len(self.files_by_country)}")
        print(f"Total processed files: {self.processed_files}")
        
        if self.failed_files:
            print(f"\nFiles where country extraction failed:")
            for file_name in sorted(self.failed_files):
                print(f"  - {file_name}")
    
    def print_country_files(self, country_name):
        """
        Print all files for a specific country
        
        Args:
            country_name (str): Name of the country to filter by
        """
        files = self.get_files_by_country(country_name)
        
        if files:
            print(f"\nFiles for {country_name}: {len(files)} files")
            print("-" * 40)
            for file_name in sorted(files):
                print(f"  - {file_name}")
        else:
            print(f"\nNo files found for country: {country_name}")
            available_countries = self.list_all_countries()
            if available_countries:
                print(f"Available countries: {', '.join(available_countries)}")


def main():
    """
    Main function to handle command line arguments and run the filter
    """
    parser = argparse.ArgumentParser(description='Filter HTML files by country')
    parser.add_argument('--country', '-c', type=str, help='Filter files by specific country')
    parser.add_argument('--list-countries', '-l', action='store_true', help='List all available countries')
    parser.add_argument('--summary', '-s', action='store_true', help='Show summary of all files grouped by country')
    parser.add_argument('--folder', '-f', type=str, default='1', help='Folder path containing HTML files (default: 1)')
    
    args = parser.parse_args()
    
    # Create filter instance and process files
    filter_instance = HTMLCountryFilter(args.folder)
    filter_instance.process_all_files()
    
    # Handle different command line options
    if args.list_countries:
        countries = filter_instance.list_all_countries()
        print(f"\nAvailable countries ({len(countries)}):")
        for country in countries:
            count = len(filter_instance.files_by_country[country])
            print(f"  - {country} ({count} files)")
    
    elif args.country:
        filter_instance.print_country_files(args.country)
    
    elif args.summary:
        filter_instance.print_summary()
    
    else:
        # Default behavior: show summary
        filter_instance.print_summary()


if __name__ == "__main__":
    main()
